import Docker from 'dockerode';

class CodeExecutor {
  constructor(containerManager) {
    this.docker = new Docker();
    this.containerManager = containerManager;
  }

  async executeCommand(userId, command, workingDir = '/home/<USER>', timeout = 30000) {
    try {
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) {
        throw new Error('用户容器不存在');
      }

      const container = this.docker.getContainer(containerInfo.containerId);

      // 创建执行配置
      const execConfig = {
        Cmd: ['sh', '-c', `cd ${workingDir} && ${command}`],
        AttachStdout: true,
        AttachStderr: true,
        AttachStdin: false
      };

      console.log(`执行命令: ${command}`);
      console.log(`工作目录: ${workingDir}`);

      const exec = await container.exec(execConfig);
      const stream = await exec.start();

      return new Promise((resolve, reject) => {
        let stdout = '';
        let stderr = '';
        let timeoutId;

        // 设置超时
        if (timeout > 0) {
          timeoutId = setTimeout(() => {
            stream.destroy();
            reject(new Error(`命令执行超时 (${timeout}ms)`));
          }, timeout);
        }

        stream.on('data', (chunk) => {
          const data = chunk.toString();
          // Docker exec 输出包含控制字符，需要处理
          if (chunk[0] === 1) { // stdout
            stdout += data.slice(8);
          } else if (chunk[0] === 2) { // stderr
            stderr += data.slice(8);
          }
        });

        stream.on('end', async () => {
          if (timeoutId) clearTimeout(timeoutId);

          try {
            const inspectResult = await exec.inspect();
            const exitCode = inspectResult.ExitCode;

            resolve({
              success: exitCode === 0,
              exitCode,
              stdout: stdout.trim(),
              stderr: stderr.trim(),
              command,
              workingDir
            });
          } catch (error) {
            resolve({
              success: false,
              exitCode: -1,
              stdout: stdout.trim(),
              stderr: stderr.trim() || error.message,
              command,
              workingDir
            });
          }
        });

        stream.on('error', (error) => {
          if (timeoutId) clearTimeout(timeoutId);
          reject(error);
        });
      });
    } catch (error) {
      console.error('执行命令失败:', error);
      return {
        success: false,
        exitCode: -1,
        stdout: '',
        stderr: error.message,
        command,
        workingDir
      };
    }
  }

  async runPythonCode(userId, code, filename = null) {
    try {
      const actualFilename = filename || `temp_${Date.now()}.py`;
      const filePath = `/home/<USER>/${actualFilename}`;

      // 首先将代码写入文件
      const writeResult = await this.writeCodeToFile(userId, filePath, code);
      if (!writeResult.success) {
        return writeResult;
      }

      // 执行 Python 代码
      const result = await this.executeCommand(userId, `python3 ${actualFilename}`, '/home/<USER>');

      return {
        ...result,
        language: 'python',
        filename: actualFilename,
        filePath
      };
    } catch (error) {
      console.error('运行Python代码失败:', error);
      return {
        success: false,
        error: error.message,
        language: 'python'
      };
    }
  }

  async runJavaScriptCode(userId, code, filename = null) {
    try {
      const actualFilename = filename || `temp_${Date.now()}.js`;
      const filePath = `/home/<USER>/${actualFilename}`;

      // 首先将代码写入文件
      const writeResult = await this.writeCodeToFile(userId, filePath, code);
      if (!writeResult.success) {
        return writeResult;
      }

      // 执行 JavaScript 代码
      const result = await this.executeCommand(userId, `node ${actualFilename}`, '/home/<USER>');

      return {
        ...result,
        language: 'javascript',
        filename: actualFilename,
        filePath
      };
    } catch (error) {
      console.error('运行JavaScript代码失败:', error);
      return {
        success: false,
        error: error.message,
        language: 'javascript'
      };
    }
  }

  async runBashScript(userId, script, filename = null) {
    try {
      const actualFilename = filename || `temp_${Date.now()}.sh`;
      const filePath = `/home/<USER>/${actualFilename}`;

      // 首先将脚本写入文件
      const writeResult = await this.writeCodeToFile(userId, filePath, script);
      if (!writeResult.success) {
        return writeResult;
      }

      // 给脚本添加执行权限
      await this.executeCommand(userId, `chmod +x ${actualFilename}`, '/home/<USER>');

      // 执行 Bash 脚本
      const result = await this.executeCommand(userId, `./${actualFilename}`, '/home/<USER>');

      return {
        ...result,
        language: 'bash',
        filename: actualFilename,
        filePath
      };
    } catch (error) {
      console.error('运行Bash脚本失败:', error);
      return {
        success: false,
        error: error.message,
        language: 'bash'
      };
    }
  }

  async writeCodeToFile(userId, filePath, content) {
    try {
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) {
        throw new Error('用户容器不存在');
      }

      const container = this.docker.getContainer(containerInfo.containerId);

      // 使用 base64 编码来避免特殊字符问题
      const base64Content = Buffer.from(content).toString('base64');
      const writeCommand = `echo '${base64Content}' | base64 -d > ${filePath}`;

      const writeExec = await container.exec({
        Cmd: ['sh', '-c', writeCommand],
        AttachStdout: true,
        AttachStderr: true
      });

      const stream = await writeExec.start();

      // 等待命令执行完成
      await new Promise((resolve, reject) => {
        let output = '';
        let error = '';

        stream.on('data', (chunk) => {
          const data = chunk.toString();
          if (chunk[0] === 1) { // stdout
            output += data.slice(8);
          } else if (chunk[0] === 2) { // stderr
            error += data.slice(8);
          }
        });

        stream.on('end', () => {
          if (error && error.trim()) {
            reject(new Error(error));
          } else {
            resolve();
          }
        });

        stream.on('error', reject);
      });

      // 自动配置环境
      await this.autoConfigureEnvironment(userId, filePath, content);

      return { success: true, path: filePath };
    } catch (error) {
      console.error('写入代码文件失败:', error);
      return { success: false, error: error.message };
    }
  }

  async autoConfigureEnvironment(userId, filePath, content) {
    try {
      const fileExtension = filePath.split('.').pop().toLowerCase();
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) return;

      const container = this.docker.getContainer(containerInfo.containerId);

      console.log(`🔧 正在为 ${fileExtension} 文件配置环境...`);

      switch (fileExtension) {
        case 'py':
          await this.configurePythonEnvironment(container, content);
          break;
        case 'js':
          await this.configureNodeEnvironment(container, content);
          break;
        case 'sh':
          await this.configureBashEnvironment(container, filePath);
          break;
        default:
          console.log(`ℹ️ ${fileExtension} 文件无需特殊环境配置`);
      }
    } catch (error) {
      console.error('环境配置失败:', error.message);
    }
  }

  async configurePythonEnvironment(container, content) {
    try {
      // 检查是否需要安装特定的Python包
      const imports = this.extractPythonImports(content);
      const packagesToInstall = this.getPythonPackagesToInstall(imports);

      if (packagesToInstall.length > 0) {
        console.log(`📦 检测到需要安装的Python包: ${packagesToInstall.join(', ')}`);

        for (const pkg of packagesToInstall) {
          console.log(`📥 安装 ${pkg}...`);
          const installExec = await container.exec({
            Cmd: ['pip3', 'install', pkg],
            AttachStdout: true,
            AttachStderr: true
          });
          await installExec.start();
        }
        console.log('✅ Python环境配置完成');
      }
    } catch (error) {
      console.error('Python环境配置失败:', error.message);
    }
  }

  async configureNodeEnvironment(container, content) {
    try {
      // 检查是否需要安装npm包
      const requires = this.extractNodeRequires(content);
      const packagesToInstall = this.getNodePackagesToInstall(requires);

      if (packagesToInstall.length > 0) {
        console.log(`📦 检测到需要安装的Node.js包: ${packagesToInstall.join(', ')}`);

        // 初始化package.json如果不存在
        const initExec = await container.exec({
          Cmd: ['sh', '-c', 'cd /home/<USER>'],
          AttachStdout: true,
          AttachStderr: true
        });
        await initExec.start();

        for (const pkg of packagesToInstall) {
          console.log(`📥 安装 ${pkg}...`);
          const installExec = await container.exec({
            Cmd: ['sh', '-c', `cd /home/<USER>
            AttachStdout: true,
            AttachStderr: true
          });
          await installExec.start();
        }
        console.log('✅ Node.js环境配置完成');
      }
    } catch (error) {
      console.error('Node.js环境配置失败:', error.message);
    }
  }

  async configureBashEnvironment(container, filePath) {
    try {
      // 给shell脚本添加执行权限
      const chmodExec = await container.exec({
        Cmd: ['chmod', '+x', filePath],
        AttachStdout: true,
        AttachStderr: true
      });
      await chmodExec.start();
      console.log('📜 Bash脚本权限配置完成');
    } catch (error) {
      console.error('Bash环境配置失败:', error.message);
    }
  }

  extractPythonImports(content) {
    const importRegex = /^(?:from\s+(\S+)\s+)?import\s+(\S+)/gm;
    const imports = [];
    let match;

    while ((match = importRegex.exec(content)) !== null) {
      if (match[1]) {
        imports.push(match[1]); // from xxx import
      } else {
        imports.push(match[2]); // import xxx
      }
    }

    return imports;
  }

  getPythonPackagesToInstall(imports) {
    const commonPackages = {
      'requests': 'requests',
      'numpy': 'numpy',
      'pandas': 'pandas',
      'matplotlib': 'matplotlib',
      'scipy': 'scipy',
      'sklearn': 'scikit-learn',
      'cv2': 'opencv-python',
      'PIL': 'Pillow',
      'flask': 'flask',
      'django': 'django',
      'fastapi': 'fastapi',
      'sqlalchemy': 'sqlalchemy'
    };

    return imports
      .filter(imp => commonPackages[imp])
      .map(imp => commonPackages[imp]);
  }

  extractNodeRequires(content) {
    const requireRegex = /require\(['"`]([^'"`]+)['"`]\)/g;
    const imports = [];
    let match;

    while ((match = requireRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }

    return imports;
  }

  getNodePackagesToInstall(requires) {
    const builtinModules = ['fs', 'path', 'http', 'https', 'url', 'crypto', 'os', 'util'];
    return requires.filter(req => !builtinModules.includes(req) && !req.startsWith('./') && !req.startsWith('../'));
  }

  async installPackage(userId, packageManager, packageName) {
    try {
      let command;

      switch (packageManager.toLowerCase()) {
        case 'pip':
        case 'pip3':
          command = `pip3 install ${packageName}`;
          break;
        case 'npm':
          command = `npm install ${packageName}`;
          break;
        case 'apt':
          command = `apt-get update && apt-get install -y ${packageName}`;
          break;
        default:
          throw new Error(`不支持的包管理器: ${packageManager}`);
      }

      console.log(`安装包: ${packageName} (使用 ${packageManager})`);
      const result = await this.executeCommand(userId, command, '/home/<USER>', 120000); // 2分钟超时

      return {
        ...result,
        packageManager,
        packageName
      };
    } catch (error) {
      console.error('安装包失败:', error);
      return {
        success: false,
        error: error.message,
        packageManager,
        packageName
      };
    }
  }

  async runFile(userId, filename, args = '') {
    try {
      const extension = filename.split('.').pop().toLowerCase();
      let command;

      switch (extension) {
        case 'py':
          command = `python3 ${filename} ${args}`;
          break;
        case 'js':
          command = `node ${filename} ${args}`;
          break;
        case 'sh':
          command = `chmod +x ${filename} && ./${filename} ${args}`;
          break;
        case 'java':
          const className = filename.replace('.java', '');
          command = `javac ${filename} && java ${className} ${args}`;
          break;
        case 'cpp':
        case 'cc':
          const execName = filename.replace(/\.(cpp|cc)$/, '');
          command = `g++ -o ${execName} ${filename} && ./${execName} ${args}`;
          break;
        case 'c':
          const cExecName = filename.replace('.c', '');
          command = `gcc -o ${cExecName} ${filename} && ./${cExecName} ${args}`;
          break;
        default:
          throw new Error(`不支持的文件类型: ${extension}`);
      }

      console.log(`运行文件: ${filename}`);
      const result = await this.executeCommand(userId, command, '/home/<USER>');

      return {
        ...result,
        filename,
        extension,
        args
      };
    } catch (error) {
      console.error('运行文件失败:', error);
      return {
        success: false,
        error: error.message,
        filename
      };
    }
  }

  async getSystemInfo(userId) {
    try {
      const commands = [
        { name: 'os', command: 'uname -a' },
        { name: 'python', command: 'python3 --version' },
        { name: 'node', command: 'node --version' },
        { name: 'npm', command: 'npm --version' },
        { name: 'java', command: 'java -version' },
        { name: 'gcc', command: 'gcc --version | head -1' },
        { name: 'disk', command: 'df -h /home/<USER>' },
        { name: 'memory', command: 'free -h' }
      ];

      const results = {};

      for (const cmd of commands) {
        const result = await this.executeCommand(userId, cmd.command, '/home/<USER>', 5000);
        results[cmd.name] = {
          available: result.success,
          output: result.success ? result.stdout : result.stderr
        };
      }

      return { success: true, systemInfo: results };
    } catch (error) {
      console.error('获取系统信息失败:', error);
      return { success: false, error: error.message };
    }
  }
}

export default CodeExecutor;