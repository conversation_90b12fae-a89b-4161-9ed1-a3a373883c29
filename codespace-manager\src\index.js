#!/usr/bin/env node

import inquirer from 'inquirer';
import chalk from 'chalk';
import UserManager from './modules/userManager.js';
import ContainerManager from './modules/containerManager.js';
import AIManager from './modules/aiManager.js';
import FileManager from './modules/fileManager.js';
import CodeExecutor from './modules/codeExecutor.js';

class CodeSpaceManager {
  constructor() {
    this.userManager = new UserManager();
    this.containerManager = new ContainerManager();
    this.aiManager = new AIManager();
    this.fileManager = new FileManager(this.containerManager);
    this.codeExecutor = new CodeExecutor(this.containerManager);
    this.currentUser = null;
  }

  async start() {
    console.log(chalk.blue.bold('\n🚀 欢迎使用 CodeSpace Manager'));
    console.log(chalk.gray('基于 OpenVSCode Server 的智能容器管理系统\n'));

    // 测试AI连接
    console.log(chalk.yellow('正在测试AI连接...'));
    const aiTest = await this.aiManager.testConnection();
    if (aiTest.success) {
      console.log(chalk.green('✅ AI连接成功'));
    } else {
      console.log(chalk.red('❌ AI连接失败:', aiTest.error));
    }

    while (true) {
      try {
        if (!this.currentUser) {
          await this.showAuthMenu();
        } else {
          await this.showMainMenu();
        }
      } catch (error) {
        console.error(chalk.red('发生错误:', error.message));
        await this.pressEnterToContinue();
      }
    }
  }

  async showAuthMenu() {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '请选择操作:',
        choices: [
          { name: '🔐 登录', value: 'login' },
          { name: '📝 注册', value: 'register' },
          { name: '❌ 退出', value: 'exit' }
        ]
      }
    ]);

    switch (action) {
      case 'login':
        await this.handleLogin();
        break;
      case 'register':
        await this.handleRegister();
        break;
      case 'exit':
        console.log(chalk.blue('再见！'));
        process.exit(0);
    }
  }

  async handleLogin() {
    const { username, password } = await inquirer.prompt([
      {
        type: 'input',
        name: 'username',
        message: '用户名:',
        validate: input => input.trim() !== '' || '用户名不能为空'
      },
      {
        type: 'password',
        name: 'password',
        message: '密码:',
        validate: input => input.trim() !== '' || '密码不能为空'
      }
    ]);

    try {
      const result = await this.userManager.loginUser(username, password);
      if (result.success) {
        this.currentUser = result.user;
        console.log(chalk.green(`✅ 登录成功！欢迎回来，${username}`));

        // 检查或创建用户容器
        await this.ensureUserContainer();
      }
    } catch (error) {
      console.log(chalk.red('❌ 登录失败:', error.message));
      await this.pressEnterToContinue();
    }
  }

  async handleRegister() {
    const { username, password, confirmPassword } = await inquirer.prompt([
      {
        type: 'input',
        name: 'username',
        message: '用户名:',
        validate: input => input.trim() !== '' || '用户名不能为空'
      },
      {
        type: 'password',
        name: 'password',
        message: '密码:',
        validate: input => input.length >= 6 || '密码至少6位'
      },
      {
        type: 'password',
        name: 'confirmPassword',
        message: '确认密码:',
        validate: (input, answers) => input === answers.password || '密码不匹配'
      }
    ]);

    try {
      const result = await this.userManager.registerUser(username, password);
      if (result.success) {
        console.log(chalk.green(`✅ 注册成功！用户ID: ${result.userId}`));
        console.log(chalk.yellow('请使用新账户登录'));
      }
    } catch (error) {
      console.log(chalk.red('❌ 注册失败:', error.message));
    }

    await this.pressEnterToContinue();
  }

  async ensureUserContainer() {
    console.log(chalk.yellow('正在检查容器状态...'));

    try {
      // 首先检查是否已有容器记录
      const existingContainer = await this.containerManager.getUserContainer(this.currentUser.id);

      if (existingContainer) {
        console.log(chalk.cyan(`📦 发现现有容器: ${existingContainer.containerId.substring(0, 12)}`));

        // 检查容器是否还在运行
        const containerStatus = await this.containerManager.getContainerStatus(this.currentUser.id);

        if (containerStatus.exists && containerStatus.running) {
          console.log(chalk.green(`✅ 容器已在运行`));
          console.log(chalk.blue(`🌐 访问地址: http://localhost:${existingContainer.port}`));
          this.currentUser.containerId = existingContainer.containerId;
          return;
        } else if (containerStatus.exists && !containerStatus.running) {
          console.log(chalk.yellow('🔄 启动现有容器...'));
          const startResult = await this.containerManager.startUserContainer(this.currentUser.id);
          if (startResult.success) {
            console.log(chalk.green(`✅ 容器已启动`));
            console.log(chalk.blue(`🌐 访问地址: http://localhost:${startResult.port}`));
            this.currentUser.containerId = existingContainer.containerId;
            return;
          } else {
            console.log(chalk.yellow('⚠️ 启动失败，将创建新容器'));
          }
        } else {
          console.log(chalk.yellow('⚠️ 容器不存在，将创建新容器'));
        }
      } else {
        console.log(chalk.cyan('🆕 首次登录，创建新容器...'));
      }

      // 创建新容器
      const containerInfo = await this.containerManager.createUserContainer(
        this.currentUser.id,
        this.currentUser.username
      );

      if (containerInfo) {
        console.log(chalk.green(`✅ 容器创建成功: ${containerInfo.containerId.substring(0, 12)}`));
        console.log(chalk.blue(`🌐 访问地址: http://localhost:${containerInfo.port}`));

        // 更新用户的容器ID
        await this.userManager.updateUserContainer(this.currentUser.username, containerInfo.containerId);
        this.currentUser.containerId = containerInfo.containerId;
      }
    } catch (error) {
      console.log(chalk.red('❌ 容器操作失败:', error.message));
    }
  }

  async showMainMenu() {
    console.log(chalk.cyan(`\n👤 当前用户: ${this.currentUser.username}`));
    console.log(chalk.gray(`🆔 用户ID: ${this.currentUser.id}`));

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '请选择操作:',
        choices: [
          { name: '🤖 AI助手', value: 'ai' },
          { name: '📁 文件管理', value: 'files' },
          { name: '⚡ 代码执行', value: 'execute' },
          { name: '🌐 打开VSCode', value: 'vscode' },
          { name: '🔧 系统信息', value: 'system' },
          { name: '🚪 退出登录', value: 'logout' }
        ]
      }
    ]);

    switch (action) {
      case 'ai':
        await this.showAIMenu();
        break;
      case 'files':
        await this.showFileMenu();
        break;
      case 'execute':
        await this.showExecuteMenu();
        break;
      case 'vscode':
        await this.openVSCode();
        break;
      case 'system':
        await this.showSystemInfo();
        break;
      case 'logout':
        this.currentUser = null;
        console.log(chalk.yellow('已退出登录'));
        break;
    }
  }

  async showAIMenu() {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '选择AI功能:',
        choices: [
          { name: '💬 自由对话', value: 'chat' },
          { name: '📝 生成代码', value: 'generate' },
          { name: '🔙 返回主菜单', value: 'back' }
        ]
      }
    ]);

    switch (action) {
      case 'chat':
        await this.handleAIChat();
        break;
      case 'generate':
        await this.handleCodeGeneration();
        break;
      case 'back':
        return;
    }
  }

  async handleAIChat() {
    const { prompt } = await inquirer.prompt([
      {
        type: 'input',
        name: 'prompt',
        message: '请输入您的问题:',
        validate: input => input.trim() !== '' || '问题不能为空'
      }
    ]);

    console.log(chalk.yellow('AI正在思考...'));
    const result = await this.aiManager.generateResponse(prompt);

    if (result.success) {
      console.log(chalk.green('\n🤖 AI回复:'));
      console.log(chalk.white(result.response));
    } else {
      console.log(chalk.red('❌ AI请求失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async handleCodeGeneration() {
    const { description, language } = await inquirer.prompt([
      {
        type: 'input',
        name: 'description',
        message: '请描述您想要的代码功能:',
        validate: input => input.trim() !== '' || '描述不能为空'
      },
      {
        type: 'list',
        name: 'language',
        message: '选择编程语言:',
        choices: ['python', 'javascript', 'java', 'cpp', 'bash']
      }
    ]);

    console.log(chalk.yellow('AI正在生成代码...'));
    const result = await this.aiManager.generateCodeFromDescription(description, language);

    if (result.success) {
      console.log(chalk.green('\n💻 生成的代码:'));
      console.log(chalk.white(result.response));

      const { saveCode } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'saveCode',
          message: '是否保存代码到容器中?',
          default: false
        }
      ]);

      if (saveCode) {
        await this.saveGeneratedCode(result.response, language);
      }
    } else {
      console.log(chalk.red('❌ 代码生成失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async saveGeneratedCode(code, language) {
    const extensions = {
      python: 'py',
      javascript: 'js',
      java: 'java',
      cpp: 'cpp',
      bash: 'sh'
    };

    const { filename } = await inquirer.prompt([
      {
        type: 'input',
        name: 'filename',
        message: `文件名 (不含扩展名):`,
        default: `generated_${Date.now()}`,
        validate: input => input.trim() !== '' || '文件名不能为空'
      }
    ]);

    const fullFilename = `${filename}.${extensions[language]}`;
    const result = await this.fileManager.writeFile(this.currentUser.id, `/home/<USER>/${fullFilename}`, code);

    if (result.success) {
      console.log(chalk.green(`✅ 代码已保存为: ${fullFilename}`));
    } else {
      console.log(chalk.red('❌ 保存失败:', result.error));
    }
  }

  async showFileMenu() {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '选择文件操作:',
        choices: [
          { name: '📋 列出文件', value: 'list' },
          { name: '👀 查看文件', value: 'read' },
          { name: '✏️ 编辑文件', value: 'write' },
          { name: '📁 创建目录', value: 'mkdir' },
          { name: '🔙 返回主菜单', value: 'back' }
        ]
      }
    ]);

    switch (action) {
      case 'list':
        await this.listFiles();
        break;
      case 'read':
        await this.readFile();
        break;
      case 'write':
        await this.writeFile();
        break;
      case 'mkdir':
        await this.createDirectory();
        break;
      case 'back':
        return;
    }
  }

  async listFiles() {
    const { path } = await inquirer.prompt([
      {
        type: 'input',
        name: 'path',
        message: '目录路径:',
        default: '/home/<USER>'
      }
    ]);

    console.log(chalk.yellow('正在列出文件...'));
    const result = await this.fileManager.listFiles(this.currentUser.id, path);

    if (result.success) {
      console.log(chalk.green(`\n📁 目录: ${result.path}`));
      if (result.files.length === 0) {
        console.log(chalk.gray('目录为空'));
      } else {
        result.files.forEach(file => {
          const icon = file.isDirectory ? '📁' : '📄';
          const color = file.isDirectory ? chalk.blue : chalk.white;
          console.log(color(`${icon} ${file.name} (${file.size})`));
        });
      }
    } else {
      console.log(chalk.red('❌ 列出文件失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async readFile() {
    const { filePath } = await inquirer.prompt([
      {
        type: 'input',
        name: 'filePath',
        message: '文件路径:',
        validate: input => input.trim() !== '' || '文件路径不能为空'
      }
    ]);

    console.log(chalk.yellow('正在读取文件...'));
    const result = await this.fileManager.readFile(this.currentUser.id, filePath);

    if (result.success) {
      console.log(chalk.green(`\n📄 文件内容: ${result.path}`));
      console.log(chalk.white('─'.repeat(50)));
      console.log(result.content);
      console.log(chalk.white('─'.repeat(50)));
    } else {
      console.log(chalk.red('❌ 读取文件失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async writeFile() {
    const { filePath, content } = await inquirer.prompt([
      {
        type: 'input',
        name: 'filePath',
        message: '文件路径:',
        validate: input => input.trim() !== '' || '文件路径不能为空'
      },
      {
        type: 'editor',
        name: 'content',
        message: '文件内容 (将打开编辑器):'
      }
    ]);

    console.log(chalk.yellow('正在写入文件...'));
    const result = await this.fileManager.writeFile(this.currentUser.id, filePath, content);

    if (result.success) {
      console.log(chalk.green(`✅ 文件写入成功: ${result.path}`));
    } else {
      console.log(chalk.red('❌ 写入文件失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async createDirectory() {
    const { dirPath } = await inquirer.prompt([
      {
        type: 'input',
        name: 'dirPath',
        message: '目录路径:',
        validate: input => input.trim() !== '' || '目录路径不能为空'
      }
    ]);

    console.log(chalk.yellow('正在创建目录...'));
    const result = await this.fileManager.createDirectory(this.currentUser.id, `/home/<USER>/${dirPath}`);

    if (result.success) {
      console.log(chalk.green(`✅ 目录创建成功: ${result.path}`));
    } else {
      console.log(chalk.red('❌ 创建目录失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async showExecuteMenu() {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '选择执行操作:',
        choices: [
          { name: '⚡ 执行命令', value: 'command' },
          { name: '📄 运行文件', value: 'file' },
          { name: '📦 安装包', value: 'install' },
          { name: '🔙 返回主菜单', value: 'back' }
        ]
      }
    ]);

    switch (action) {
      case 'command':
        await this.executeCommand();
        break;
      case 'file':
        await this.runFile();
        break;
      case 'install':
        await this.installPackage();
        break;
      case 'back':
        return;
    }
  }

  async executeCommand() {
    const { command, workingDir } = await inquirer.prompt([
      {
        type: 'input',
        name: 'command',
        message: '要执行的命令:',
        validate: input => input.trim() !== '' || '命令不能为空'
      },
      {
        type: 'input',
        name: 'workingDir',
        message: '工作目录:',
        default: '/home/<USER>'
      }
    ]);

    console.log(chalk.yellow('正在执行命令...'));
    const result = await this.codeExecutor.executeCommand(this.currentUser.id, command, workingDir);

    this.displayExecutionResult(result);
    await this.pressEnterToContinue();
  }

  displayExecutionResult(result) {
    console.log(chalk.cyan('\n📊 执行结果:'));
    console.log(chalk.gray(`命令: ${result.command}`));
    console.log(chalk.gray(`工作目录: ${result.workingDir}`));
    console.log(chalk.gray(`退出码: ${result.exitCode}`));
    console.log(chalk.gray(`状态: ${result.success ? '✅ 成功' : '❌ 失败'}`));

    if (result.stdout) {
      console.log(chalk.green('\n📤 标准输出:'));
      console.log(result.stdout);
    }

    if (result.stderr) {
      console.log(chalk.red('\n📥 错误输出:'));
      console.log(result.stderr);
    }
  }

  async runFile() {
    // 首先列出当前目录的文件
    console.log(chalk.yellow('正在获取文件列表...'));
    const fileListResult = await this.fileManager.listFiles(this.currentUser.id, '/home/<USER>');

    if (!fileListResult.success) {
      console.log(chalk.red('❌ 获取文件列表失败:', fileListResult.error));
      await this.pressEnterToContinue();
      return;
    }

    const executableFiles = fileListResult.files.filter(file =>
      !file.isDirectory &&
      (file.name.endsWith('.py') ||
       file.name.endsWith('.js') ||
       file.name.endsWith('.java') ||
       file.name.endsWith('.cpp') ||
       file.name.endsWith('.c') ||
       file.name.endsWith('.sh'))
    );

    if (executableFiles.length === 0) {
      console.log(chalk.yellow('📁 当前目录没有可执行的代码文件'));
      console.log(chalk.gray('支持的文件类型: .py, .js, .java, .cpp, .c, .sh'));
      await this.pressEnterToContinue();
      return;
    }

    console.log(chalk.green('\n📁 可执行文件列表:'));
    executableFiles.forEach((file, index) => {
      const icon = this.getFileIcon(file.name);
      console.log(chalk.white(`${index + 1}. ${icon} ${file.name}`));
    });

    const { choice } = await inquirer.prompt([
      {
        type: 'list',
        name: 'choice',
        message: '选择要运行的文件:',
        choices: [
          ...executableFiles.map((file, index) => ({
            name: `${this.getFileIcon(file.name)} ${file.name}`,
            value: file.name
          })),
          { name: '🔙 返回', value: 'back' }
        ]
      }
    ]);

    if (choice === 'back') return;

    const { args } = await inquirer.prompt([
      {
        type: 'input',
        name: 'args',
        message: '命令行参数 (可选):',
        default: ''
      }
    ]);

    console.log(chalk.yellow(`正在运行文件: ${choice}`));
    const result = await this.codeExecutor.runFile(this.currentUser.id, choice, args);

    this.displayExecutionResult(result);
    await this.pressEnterToContinue();
  }

  getFileIcon(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    const icons = {
      'py': '🐍',
      'js': '🟨',
      'java': '☕',
      'cpp': '⚙️',
      'c': '🔧',
      'sh': '📜'
    };
    return icons[ext] || '📄';
  }

  async installPackage() {
    const { packageManager, packageName } = await inquirer.prompt([
      {
        type: 'list',
        name: 'packageManager',
        message: '选择包管理器:',
        choices: [
          { name: '🐍 pip (Python)', value: 'pip' },
          { name: '📦 npm (Node.js)', value: 'npm' },
          { name: '🔧 apt (系统包)', value: 'apt' }
        ]
      },
      {
        type: 'input',
        name: 'packageName',
        message: '包名:',
        validate: input => input.trim() !== '' || '包名不能为空'
      }
    ]);

    console.log(chalk.yellow(`正在安装 ${packageName}...`));
    const result = await this.codeExecutor.installPackage(this.currentUser.id, packageManager, packageName);

    this.displayExecutionResult(result);
    await this.pressEnterToContinue();
  }

  async openVSCode() {
    const containerInfo = await this.containerManager.getUserContainer(this.currentUser.id);
    if (containerInfo) {
      const url = `http://localhost:${containerInfo.port}`;
      console.log(chalk.blue(`🌐 VSCode访问地址: ${url}`));
      console.log(chalk.yellow('请在浏览器中打开上述地址'));
    } else {
      console.log(chalk.red('❌ 容器不存在'));
    }

    await this.pressEnterToContinue();
  }

  async showSystemInfo() {
    console.log(chalk.yellow('正在获取系统信息...'));
    const result = await this.codeExecutor.getSystemInfo(this.currentUser.id);

    if (result.success) {
      console.log(chalk.green('\n🔧 系统信息:'));
      Object.entries(result.systemInfo).forEach(([key, info]) => {
        const status = info.available ? '✅' : '❌';
        console.log(chalk.cyan(`${status} ${key.toUpperCase()}:`));
        console.log(chalk.white(`   ${info.output}`));
      });
    } else {
      console.log(chalk.red('❌ 获取系统信息失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async pressEnterToContinue() {
    await inquirer.prompt([
      {
        type: 'input',
        name: 'continue',
        message: '按回车键继续...'
      }
    ]);
  }
}

// 启动应用
const app = new CodeSpaceManager();
app.start().catch(error => {
  console.error(chalk.red('应用启动失败:', error));
  process.exit(1);
});